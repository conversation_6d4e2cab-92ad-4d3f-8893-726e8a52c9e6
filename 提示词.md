1. 测试登录 - 登录成功
2. 获取报警列表

[2025/9/25 10:38:56] ⚠️ 报警列表测试结果
==================================================
{
  "success": false,
  "message": "查询异常: 认证失败：未获取到Admin-Token，请先登录",
  "data": [],
  "error": {}
}
==================================================


[2025/9/25 10:38:56] ⚠️ 开始报警列表测试
==================================================
{
  "pageSize": 10
}
==================================================


[2025/9/25 10:38:52] 🔐 登录测试结果
==================================================
{
  "success": true,
  "message": "The operation succeeded",
  "token": "eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjE0OWVjMjNkLTM3M2EtNDA0Mi1hNzNhLTI2YWU5OGEyNmU2NCJ9.S0KMmHwLc4Q00OBX-tHv4RoD3wzpWYTB1dHetnqjFEYV5X93SHkbhdNiwIUBK0Ud3w2nCHQxxCLIB6lnv-sePA",
  "data": {
    "msg": "The operation succeeded",
    "code": 200,
    "token": "eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjE0OWVjMjNkLTM3M2EtNDA0Mi1hNzNhLTI2YWU5OGEyNmU2NCJ9.S0KMmHwLc4Q00OBX-tHv4RoD3wzpWYTB1dHetnqjFEYV5X93SHkbhdNiwIUBK0Ud3w2nCHQxxCLIB6lnv-sePA"
  }
}
==================================================


[2025/9/25 10:38:52] 🔐 开始登录测试
==================================================
{
  "username": "bydq_admin",
  "sourceType": 1
}
==================================================

测试结果已清空，等待新的测试...























Bearer eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjExYWM5Njk2LWYzNDctNGM3MC05ZmMyLWYyMDc1ZTM3OWM1NyJ9.vMQFFt3wdJvU1mfHus9KCeXL64SlKvyQOJOObuoAAZNc0hiqoc5be1INZSfOPvMWR5pe4bMiZYwMjg-p--_ooA


基于接口文档，完成接口的开发，功能实现都放在  config.js 文件中，同时提供一个html页面测试。
1. 不要影响现有功能。
2. ip、端口、url提供可配置的入口，方便替换。

http://*************/scada/topo/fullscreen?guid=f166d35a-180f-4fbf-91ab-a63229da3391&type=3


ip或者域名：*************
端口：8083
http地址： http://*************/
mqtt地址：ws://*************:8083/mqtt
把以上信息存入config.json文件中，然后修改webgl目录下所有文件，替换ip和域名。



1. 删除水冷系统mqtt接入逻辑
2. 放大相关区域的字，让触摸屏版本更清晰可见·（ 不要修改style.css文件，避免影响其他页面。）
- left-panel



mqtt.qizhiyun.cc

白云电气本地端已经部署，目前遗留问题，开机自启
本地端管理后台访问地址:
http://*************/
bydq_admin/Aa123456
mqtt ip: *************
设备端mqtt port: 1883 @洪润 
客户端mqtt server：ws://*************:8083/mqtt  

# 替换规则整理
https://.*qizhiyun.cc/ 替换成 http://*************/
wss://.*qizhiyun.cc/mqtt 替换成 ws://*************:8083/mqtt